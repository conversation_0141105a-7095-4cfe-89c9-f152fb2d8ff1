import json
import os
import logging

logger = logging.getLogger(__name__)

DEFAULT_CONFIG = {
    "package_name": "com.growx.kht1",
    "activity_name": "com.jxclient.release.jxclient",
    "adb_path": r"C:\LDPlayer\LDPlayer9\adb.exe",
    # "screenshot_interval": 500,
    "loop_delay": 5,
    "auto_login": False,
    "login_retry": 3,
    # "auto_daily": False,
    # "auto_event": False,
    # "auto_dungeon": False,
    "auto_train": False,
    "log_level": "INFO",
    "save_log": True,
    # "login_btn_x": 540,
    # "login_btn_y": 1800,
    "username_x": 506,
    "username_y": 232,
    "password_x": 522,
    "password_y": 309,

    # Auto train settings
    "train_areas": {
        "khu_vuc_1": {
            "name": "<PERSON>hu vự<PERSON> luyện công 50",
            "maps": {
                "map_1": {"name": "<PERSON><PERSON><PERSON> đ<PERSON>o h<PERSON>u viện", "x": 100, "y": 200},
                "map_2": {"name": "Hưởng Thủy động", "x": 150, "y": 250}
            }
        },
        "khu_vuc_3": {
            "name": "Khu vực luyện công 70",
            "maps": {
                "map_1": {"name": "Đại Tù động", "x": 200, "y": 300},
            }
        }
    },
    "train_area": "khu_vuc_1",
    "train_map": "map_1",
    "train_x": 199,
    "train_y": 199,
    # "auto_fight_enabled": True,
    "minimap_pos": (942, 54),
    "xa_phu_pos": (137, 200)
}


def load_config(config_file: str = "settings.json") -> dict:
    """
    Load configuration from file
    """
    config = DEFAULT_CONFIG.copy()
    
    if os.path.exists(config_file):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                user_config = json.load(f)
                config.update(user_config)
        except Exception as e:
            logger.error(f"Error loading config: {str(e)}")
    
    return config


def save_config(config: dict, config_file: str = "settings.json"):
    """
    Save configuration to file
    """
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        logger.info(f"Config saved to {config_file}")
    except Exception as e:
        logger.error(f"Error saving config: {str(e)}")