import logging
from typing import <PERSON><PERSON>, Dict, Any

logger = logging.getLogger(__name__)


class ResolutionScaler:
    """
    Utility class để scale coordinates từ resolution chuẩn sang resolution thiết bị
    """
    
    def __init__(self, base_width: int = 1280, base_height: int = 720):
        """
        Initialize với resolution chuẩn (base resolution)
        """
        self.base_width = base_width
        self.base_height = base_height
        self.device_resolutions = {}  # Cache resolution của các device
        
    def get_device_resolution(self, adb, device_id: str) -> Tuple[int, int]:
        """
        Lấy resolution của device và cache lại
        """
        if device_id not in self.device_resolutions:
            width, height = adb.get_screen_size(device_id)
            self.device_resolutions[device_id] = (width, height)
            logger.info(f"Device {device_id} resolution: {width}x{height}")
        
        return self.device_resolutions[device_id]
    
    def scale_coordinates(self, adb, device_id: str, x: int, y: int) -> <PERSON><PERSON>[int, int]:
        """
        Scale coordinates từ base resolution sang device resolution
        """
        device_width, device_height = self.get_device_resolution(adb, device_id)
        
        # Tính tỷ lệ scale
        scale_x = device_width / self.base_width
        scale_y = device_height / self.base_height
        
        # Scale coordinates
        scaled_x = int(x * scale_x)
        scaled_y = int(y * scale_y)
        
        logger.debug(f"Scaled ({x}, {y}) -> ({scaled_x}, {scaled_y}) for device {device_id}")
        return scaled_x, scaled_y
    
    def get_scale_factors(self, adb, device_id: str) -> Tuple[float, float]:
        """
        Lấy scale factors cho device
        """
        device_width, device_height = self.get_device_resolution(adb, device_id)
        scale_x = device_width / self.base_width
        scale_y = device_height / self.base_height
        return scale_x, scale_y
    
    def is_scaling_needed(self, adb, device_id: str) -> bool:
        """
        Kiểm tra có cần scaling không
        """
        device_width, device_height = self.get_device_resolution(adb, device_id)
        return device_width != self.base_width or device_height != self.base_height
    
    def get_resolution_info(self, adb, device_id: str) -> Dict[str, Any]:
        """
        Lấy thông tin chi tiết về resolution
        """
        device_width, device_height = self.get_device_resolution(adb, device_id)
        scale_x, scale_y = self.get_scale_factors(adb, device_id)
        return {
            'device_id': device_id,
            'base_resolution': f"{self.base_width}x{self.base_height}",
            'device_resolution': f"{device_width}x{device_height}",
            'scale_x': scale_x,
            'scale_y': scale_y,
            'scaling_needed': self.is_scaling_needed(adb, device_id)
        }


# Global instance
resolution_scaler = ResolutionScaler()
