from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                             QPushButton, QTableWidget, QTableWidgetItem, QTabWidget,
                             QTextEdit, QGroupBox, QLabel, QComboBox, QMessageBox,
                             QHeaderView, QMenu, QAction)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread
from PyQt5.QtGui import QIcon, QFont
import logging
from datetime import datetime
from ..core.adb_controller import ADBController
from ..core.account_manager import AccountManager, AccountStatus
from ..utils.resolution_utils import resolution_scaler
from .account_dialog import AccountDialog
from .settings_dialog import SettingsDialog
from ..tasks.auto_task import auto_task

logger = logging.getLogger(__name__)


class MainWindow(QMainWindow):
    update_log_signal = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        self.adb = ADBController()
        self.account_manager = AccountManager(self.adb)
        self.account_manager.register_callback(self.on_account_event)

        self.init_ui()
        self.init_timers()

        # Refresh devices
        self.refresh_devices()

        # Load và hiển thị accounts đã lưu
        self.update_account_table()
        
    def init_ui(self):
        self.setWindowTitle("Auto VLTK - Võ Lâm Truyền Kỳ Tool")
        self.setGeometry(100, 100, 1200, 800)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        
        # Control panel
        control_panel = self.create_control_panel()
        main_layout.addWidget(control_panel)
        
        # Main content
        content_layout = QHBoxLayout()
        
        # Account table
        account_widget = self.create_account_widget()
        content_layout.addWidget(account_widget, 3)
        
        # Info panel
        info_widget = self.create_info_widget()
        content_layout.addWidget(info_widget, 1)
        
        main_layout.addLayout(content_layout)
        
        # Log panel
        log_widget = self.create_log_widget()
        main_layout.addWidget(log_widget)
        
        self.update_log_signal.connect(self.append_log)
        
    def create_control_panel(self):
        group = QGroupBox("Điều khiển")
        layout = QHBoxLayout()
        
        self.btn_refresh = QPushButton("Làm mới thiết bị")
        self.btn_refresh.clicked.connect(self.refresh_devices)
        
        self.btn_add_account = QPushButton("Thêm tài khoản")
        self.btn_add_account.clicked.connect(self.add_account)
        
        self.btn_start_all = QPushButton("Chạy tất cả")
        self.btn_start_all.clicked.connect(self.start_all_accounts)
        
        self.btn_stop_all = QPushButton("Dừng tất cả")
        self.btn_stop_all.clicked.connect(self.stop_all_accounts)
        
        self.btn_settings = QPushButton("Cài đặt")
        self.btn_settings.clicked.connect(self.show_settings)
        
        layout.addWidget(self.btn_refresh)
        layout.addWidget(self.btn_add_account)
        layout.addWidget(self.btn_start_all)
        layout.addWidget(self.btn_stop_all)
        layout.addWidget(self.btn_settings)
        layout.addStretch()
        
        group.setLayout(layout)
        return group
    
    def create_account_widget(self):
        group = QGroupBox("Danh sách tài khoản")
        layout = QVBoxLayout()
        
        self.account_table = QTableWidget()
        # self.account_table.setColumnCount(8)
        # self.account_table.setHorizontalHeaderLabels([
        #     "ID", "Tên", "Thiết bị", "Tài khoản", "Server", 
        #     "Nhân vật", "Trạng thái", "Hành động"
        # ])

        self.account_table.setColumnCount(6)
        self.account_table.setHorizontalHeaderLabels([
            "ID", "Tên", "Thiết bị", "Tài khoản", "Trạng thái", "Hành động"
        ])
        
        header = self.account_table.horizontalHeader()
        header.setSectionResizeMode(1, QHeaderView.Stretch)
        header.setSectionResizeMode(3, QHeaderView.Stretch)
        
        self.account_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.account_table.customContextMenuRequested.connect(self.show_account_menu)
        
        layout.addWidget(self.account_table)
        group.setLayout(layout)
        return group
    
    def create_info_widget(self):
        group = QGroupBox("Thông tin")
        layout = QVBoxLayout()
        
        self.info_tabs = QTabWidget()
        
        # Device info
        device_widget = QWidget()
        device_layout = QVBoxLayout(device_widget)
        self.device_combo = QComboBox()
        self.device_info_label = QLabel()
        device_layout.addWidget(QLabel("Thiết bị:"))
        device_layout.addWidget(self.device_combo)
        device_layout.addWidget(self.device_info_label)
        device_layout.addStretch()
        
        # Statistics
        stats_widget = QWidget()
        stats_layout = QVBoxLayout(stats_widget)
        self.stats_label = QLabel()
        stats_layout.addWidget(self.stats_label)
        stats_layout.addStretch()
        
        self.info_tabs.addTab(device_widget, "Thiết bị")
        self.info_tabs.addTab(stats_widget, "Thống kê")
        
        layout.addWidget(self.info_tabs)
        group.setLayout(layout)
        return group
    
    def create_log_widget(self):
        group = QGroupBox("Nhật ký")
        layout = QVBoxLayout()
        
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setMaximumHeight(200)
        
        layout.addWidget(self.log_text)
        group.setLayout(layout)
        return group
    
    def init_timers(self):
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.update_display)
        self.refresh_timer.start(1000)
        
        # self.device_timer = QTimer()
        # self.device_timer.timeout.connect(self.refresh_devices)
        # self.device_timer.start(5000)
    
    def refresh_devices(self):
        devices = self.adb.refresh_devices()
        self.device_combo.clear()
        self.device_combo.addItems(devices)
        self.update_device_info()
        self.log(f"Tìm thấy {len(devices)} thiết bị")
    
    def update_device_info(self):
        device_id = self.device_combo.currentText()
        if device_id:
            resolution_info = resolution_scaler.get_resolution_info(self.adb, device_id)
            scale_x, scale_y = resolution_info['scale_x'], resolution_info['scale_y']
            info_text = f"Độ phân giải: {resolution_info['device_resolution']} | Scale: {scale_x:.2f}x{scale_y:.2f}"
            self.device_info_label.setText(info_text)
    
    def add_account(self):
        devices = self.adb.devices
        if not devices:
            QMessageBox.warning(self, "Lỗi", "Không tìm thấy thiết bị nào!")
            return
        
        dialog = AccountDialog(devices, self)
        if dialog.exec_():
            account = dialog.get_account()
            if self.account_manager.add_account(account):
                self.update_account_table()
                self.log(f"Đã thêm tài khoản: {account.name}")
    
    def update_account_table(self):
        self.account_table.setRowCount(len(self.account_manager.accounts))
        
        for row, (acc_id, account) in enumerate(self.account_manager.accounts.items()):
            self.account_table.setItem(row, 0, QTableWidgetItem(account.id))
            self.account_table.setItem(row, 1, QTableWidgetItem(account.name))
            self.account_table.setItem(row, 2, QTableWidgetItem(account.device_id))
            self.account_table.setItem(row, 3, QTableWidgetItem(account.username))
            # self.account_table.setItem(row, 4, QTableWidgetItem(account.server))
            # self.account_table.setItem(row, 5, QTableWidgetItem(account.character))
            
            status_item = QTableWidgetItem(account.status.value)
            if account.status == AccountStatus.RUNNING:
                status_item.setBackground(Qt.green)
            elif account.status == AccountStatus.ERROR:
                status_item.setBackground(Qt.red)
            elif account.status == AccountStatus.PAUSED:
                status_item.setBackground(Qt.yellow)
            self.account_table.setItem(row, 4, status_item)
            
            action_widget = QWidget()
            action_layout = QHBoxLayout(action_widget)
            action_layout.setContentsMargins(0, 0, 0, 0)
            
            if account.status == AccountStatus.IDLE or account.status == AccountStatus.STOPPED:
                btn_start = QPushButton("Chạy")
                btn_start.clicked.connect(lambda checked, aid=acc_id: self.start_account(aid))
                action_layout.addWidget(btn_start)
            elif account.status == AccountStatus.RUNNING:
                btn_pause = QPushButton("Tạm dừng")
                btn_pause.clicked.connect(lambda checked, aid=acc_id: self.pause_account(aid))
                btn_stop = QPushButton("Dừng")
                btn_stop.clicked.connect(lambda checked, aid=acc_id: self.stop_account(aid))
                action_layout.addWidget(btn_pause)
                action_layout.addWidget(btn_stop)
            elif account.status == AccountStatus.PAUSED:
                btn_resume = QPushButton("Tiếp tục")
                btn_resume.clicked.connect(lambda checked, aid=acc_id: self.resume_account(aid))
                btn_stop = QPushButton("Dừng")
                btn_stop.clicked.connect(lambda checked, aid=acc_id: self.stop_account(aid))
                action_layout.addWidget(btn_resume)
                action_layout.addWidget(btn_stop)
            
            self.account_table.setCellWidget(row, 5, action_widget)
    
    def show_account_menu(self, position):
        row = self.account_table.currentRow()
        if row < 0:
            return
        
        acc_id = self.account_table.item(row, 0).text()
        menu = QMenu()
        
        edit_action = QAction("Sửa", self)
        edit_action.triggered.connect(lambda: self.edit_account(acc_id))
        menu.addAction(edit_action)
        
        delete_action = QAction("Xóa", self)
        delete_action.triggered.connect(lambda: self.delete_account(acc_id))
        menu.addAction(delete_action)
        
        menu.exec_(self.account_table.mapToGlobal(position))
    
    def edit_account(self, account_id):
        account = self.account_manager.accounts.get(account_id)
        if not account:
            return
        
        dialog = AccountDialog(self.adb.devices, self, account)
        if dialog.exec_():
            updated_account = dialog.get_account()
            self.account_manager.update_account(updated_account)
            self.update_account_table()
            self.log(f"Đã cập nhật tài khoản: {updated_account.name}")
    
    def delete_account(self, account_id):
        reply = QMessageBox.question(self, "Xác nhận", "Bạn có chắc muốn xóa tài khoản này?")
        if reply == QMessageBox.Yes:
            if self.account_manager.remove_account(account_id):
                self.update_account_table()
                self.log(f"Đã xóa tài khoản: {account_id}")
    
    def start_account(self, account_id):
        self.account_manager.start_account(account_id, auto_task)
        self.update_account_table()
    
    def stop_account(self, account_id):
        self.account_manager.stop_account(account_id)
        self.update_account_table()
    
    def pause_account(self, account_id):
        self.account_manager.pause_account(account_id)
        self.update_account_table()
    
    def resume_account(self, account_id):
        self.account_manager.resume_account(account_id)
        self.update_account_table()
    
    def start_all_accounts(self):
        for acc_id in self.account_manager.accounts:
            self.account_manager.start_account(acc_id, auto_task)
        self.update_account_table()
    
    def stop_all_accounts(self):
        for acc_id in self.account_manager.accounts:
            self.account_manager.stop_account(acc_id)
        self.update_account_table()
    
    def show_settings(self):
        dialog = SettingsDialog(self)
        dialog.exec_()
    
    def on_account_event(self, event, data):
        self.update_account_table()
        self.update_statistics()
    
    def update_display(self):
        self.update_statistics()
    
    def update_statistics(self):
        total = len(self.account_manager.accounts)
        running = sum(1 for acc in self.account_manager.accounts.values() 
                     if acc.status == AccountStatus.RUNNING)
        paused = sum(1 for acc in self.account_manager.accounts.values() 
                    if acc.status == AccountStatus.PAUSED)
        error = sum(1 for acc in self.account_manager.accounts.values() 
                   if acc.status == AccountStatus.ERROR)
        
        stats_text = f"""
        Tổng số tài khoản: {total}
        Đang chạy: {running}
        Tạm dừng: {paused}
        Lỗi: {error}
        """
        self.stats_label.setText(stats_text)
    
    def log(self, message):
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.update_log_signal.emit(f"[{timestamp}] {message}")
    
    def append_log(self, message):
        self.log_text.append(message)
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())