from PyQt5.QtWidgets import (Q<PERSON>ialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                             QLineEdit, QPushButton, QComboBox, QDialogButtonBox,
                             QTabWidget, QWidget, QSpinBox, QCheckBox, QGroupBox)
from PyQt5.QtCore import Qt
import uuid
from ..core.account_manager import Account


class AccountDialog(QDialog):
    def __init__(self, devices, parent=None, account=None):
        super().__init__(parent)
        self.devices = devices
        self.account = account
        self.init_ui()
        
        if account:
            self.load_account_data()
    
    def init_ui(self):
        self.setWindowTitle("Thêm/Sửa tài khoản")
        self.setModal(True)
        self.resize(500, 400)

        layout = QVBoxLayout()

        # Create tabs
        self.tabs = QTabWidget()

        # Account info tab
        account_tab = QWidget()
        account_layout = QFormLayout()

        self.name_edit = QLineEdit()
        account_layout.addRow("Tên hiển thị:", self.name_edit)

        self.device_combo = QComboBox()
        self.device_combo.addItems(self.devices)
        account_layout.addRow("Thiết bị:", self.device_combo)

        self.username_edit = QLineEdit()
        account_layout.addRow("Tài khoản:", self.username_edit)

        self.password_edit = QLineEdit()
        self.password_edit.setEchoMode(QLineEdit.Password)
        account_layout.addRow("Mật khẩu:", self.password_edit)

        # self.server_edit = QLineEdit()
        # account_layout.addRow("Server:", self.server_edit)

        # self.character_edit = QLineEdit()
        # account_layout.addRow("Nhân vật:", self.character_edit)

        account_tab.setLayout(account_layout)
        self.tabs.addTab(account_tab, "Thông tin tài khoản")

        # Train settings tab
        train_tab = self.create_train_tab()
        self.tabs.addTab(train_tab, "Cài đặt Train")

        layout.addWidget(self.tabs)
        
        button_box = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel,
            Qt.Horizontal, self
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        
        layout.addWidget(button_box)
        self.setLayout(layout)

    def create_train_tab(self):
        """Tạo tab cài đặt train cho account"""
        widget = QWidget()
        layout = QVBoxLayout()

        # Train area settings
        area_group = QGroupBox("Cài đặt khu vực luyện công")
        area_layout = QFormLayout()

        self.train_area_combo = QComboBox()
        # Load train areas from config
        from ..config.config import load_config
        config = load_config()
        train_areas = config.get('train_areas', {})

        for area_id, area_data in train_areas.items():
            self.train_area_combo.addItem(area_data['name'], area_id)

        area_layout.addRow("Khu vực:", self.train_area_combo)

        self.train_map_combo = QComboBox()
        self.train_area_combo.currentTextChanged.connect(self.update_train_maps)

        area_layout.addRow("Map:", self.train_map_combo)
        area_group.setLayout(area_layout)
        layout.addWidget(area_group)

        # Train position settings
        position_group = QGroupBox("Vị trí luyện công")
        position_layout = QFormLayout()

        self.train_x = QSpinBox()
        self.train_x.setRange(0, 9999)
        self.train_x.setValue(config.get('train_x'))
        position_layout.addRow("Tọa độ X:", self.train_x)

        self.train_y = QSpinBox()
        self.train_y.setRange(0, 9999)
        self.train_y.setValue(config.get('train_y'))
        position_layout.addRow("Tọa độ Y:", self.train_y)

        position_group.setLayout(position_layout)
        layout.addWidget(position_group)

        # Auto fight settings
        # fight_group = QGroupBox("Cài đặt auto đánh")
        # fight_layout = QFormLayout()

        # self.auto_fight_enabled = QCheckBox()
        # self.auto_fight_enabled.setChecked(True)
        # fight_layout.addRow("Bật auto đánh:", self.auto_fight_enabled)

        # fight_group.setLayout(fight_layout)
        # layout.addWidget(fight_group)

        layout.addStretch()
        widget.setLayout(layout)

        # Load initial maps
        self.update_train_maps()

        return widget

    def update_train_maps(self):
        """Update train maps based on selected area"""
        self.train_map_combo.clear()

        from ..config.config import load_config
        config = load_config()
        train_areas = config.get('train_areas', {})

        current_area = self.train_area_combo.currentData()
        if current_area and current_area in train_areas:
            maps = train_areas[current_area].get('maps', {})
            for map_id, map_data in maps.items():
                self.train_map_combo.addItem(map_data['name'], map_id)

    def load_account_data(self):
        self.name_edit.setText(self.account.name)

        index = self.device_combo.findText(self.account.device_id)
        if index >= 0:
            self.device_combo.setCurrentIndex(index)

        self.username_edit.setText(self.account.username)
        self.password_edit.setText(self.account.password)
        # self.server_edit.setText(self.account.server)
        # self.character_edit.setText(self.account.character)

        # Load train settings
        settings = self.account.settings
        if 'train_area' in settings:
            for i in range(self.train_area_combo.count()):
                if self.train_area_combo.itemData(i) == settings['train_area']:
                    self.train_area_combo.setCurrentIndex(i)
                    break

        self.update_train_maps()  # Update maps based on area

        if 'train_map' in settings:
            for i in range(self.train_map_combo.count()):
                if self.train_map_combo.itemData(i) == settings['train_map']:
                    self.train_map_combo.setCurrentIndex(i)
                    break

        if 'train_x' in settings:
            self.train_x.setValue(settings['train_x'])
        if 'train_y' in settings:
            self.train_y.setValue(settings['train_y'])
        # if 'auto_fight_enabled' in settings:
        #     self.auto_fight_enabled.setChecked(settings['auto_fight_enabled'])
    
    def get_account(self):
        if self.account:
            account_id = self.account.id
        else:
            account_id = str(uuid.uuid4())[:8]

        # Collect train settings
        train_settings = {
            'train_area': self.train_area_combo.currentData(),
            'train_map': self.train_map_combo.currentData(),
            'train_x': self.train_x.value(),
            'train_y': self.train_y.value(),
            # 'auto_fight_enabled': self.auto_fight_enabled.isChecked()
        }

        return Account(
            id=account_id,
            name=self.name_edit.text(),
            device_id=self.device_combo.currentText(),
            username=self.username_edit.text(),
            password=self.password_edit.text(),
            # server=self.server_edit.text(),
            # character=self.character_edit.text(),
            settings=train_settings
        )