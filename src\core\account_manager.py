import threading
import time
import logging
from typing import Dict, List, Optional, Callable
from dataclasses import dataclass, field
from enum import Enum
import json
import os

logger = logging.getLogger(__name__)


class AccountStatus(Enum):
    IDLE = "idle"
    RUNNING = "running"
    PAUSED = "paused"
    ERROR = "error"
    STOPPED = "stopped"


@dataclass
class Account:
    id: str
    name: str
    device_id: str
    username: str
    password: str
    server: str = ""
    character: str = ""
    status: AccountStatus = AccountStatus.IDLE
    last_action_time: float = 0
    error_message: str = ""
    settings: Dict = field(default_factory=dict)


class AccountManager:
    def __init__(self, adb_controller):
        self.adb = adb_controller
        self.accounts: Dict[str, Account] = {}
        self.threads: Dict[str, threading.Thread] = {}
        self.stop_events: Dict[str, threading.Event] = {}
        self.pause_events: Dict[str, threading.Event] = {}
        self.callbacks: List[Callable] = []
        self.accounts_file = "accounts.json"
        self.load_accounts()
        
    def add_account(self, account: Account) -> bool:
        if account.id in self.accounts:
            logger.error(f"Account {account.id} already exists")
            return False
        
        self.accounts[account.id] = account
        self.save_accounts()
        self._notify_callbacks("account_added", account)
        return True
    
    def remove_account(self, account_id: str) -> bool:
        if account_id not in self.accounts:
            return False
        
        self.stop_account(account_id)
        del self.accounts[account_id]
        self.save_accounts()
        self._notify_callbacks("account_removed", account_id)
        return True
    
    def update_account(self, account: Account) -> bool:
        if account.id not in self.accounts:
            return False
        
        self.accounts[account.id] = account
        self.save_accounts()
        self._notify_callbacks("account_updated", account)
        return True
    
    def start_account(self, account_id: str, task_function: Callable) -> bool:
        if account_id not in self.accounts:
            return False
        
        account = self.accounts[account_id]
        if account.status == AccountStatus.RUNNING:
            logger.warning(f"Account {account_id} is already running")
            return False
        
        stop_event = threading.Event()
        pause_event = threading.Event()
        self.stop_events[account_id] = stop_event
        self.pause_events[account_id] = pause_event
        
        thread = threading.Thread(
            target=self._run_account,
            args=(account, task_function, stop_event, pause_event),
            daemon=True
        )
        self.threads[account_id] = thread
        thread.start()
        
        account.status = AccountStatus.RUNNING
        self._notify_callbacks("account_started", account)
        return True
    
    def stop_account(self, account_id: str) -> bool:
        if account_id not in self.accounts:
            return False
        account = self.accounts[account_id]
        if account.status != AccountStatus.RUNNING:
            return False
        
        if account_id in self.stop_events:
            self.stop_events[account_id].set()
        
        if account_id in self.threads:
            self.threads[account_id].join(timeout=5)
            del self.threads[account_id]
        
        account.status = AccountStatus.STOPPED
        self._notify_callbacks("account_stopped", account)
        return True
    
    def pause_account(self, account_id: str) -> bool:
        if account_id not in self.accounts:
            return False
        
        account = self.accounts[account_id]
        if account.status != AccountStatus.RUNNING:
            return False
        
        if account_id in self.pause_events:
            self.pause_events[account_id].set()
        
        account.status = AccountStatus.PAUSED
        self._notify_callbacks("account_paused", account)
        return True
    
    def resume_account(self, account_id: str) -> bool:
        if account_id not in self.accounts:
            return False
        
        account = self.accounts[account_id]
        if account.status != AccountStatus.PAUSED:
            return False
        
        if account_id in self.pause_events:
            self.pause_events[account_id].clear()
        
        account.status = AccountStatus.RUNNING
        self._notify_callbacks("account_resumed", account)
        return True
    
    def _run_account(self, account: Account, task_function: Callable, 
                     stop_event: threading.Event, pause_event: threading.Event):
        try:
            while not stop_event.is_set():
                if pause_event.is_set():
                    time.sleep(0.5)
                    continue
                
                account.last_action_time = time.time()
                task_function(account, self.adb, stop_event, pause_event)
                
        except Exception as e:
            logger.error(f"Error in account {account.id}: {str(e)}")
            account.status = AccountStatus.ERROR
            account.error_message = str(e)
            self._notify_callbacks("account_error", account)
    
    def register_callback(self, callback: Callable):
        self.callbacks.append(callback)
    
    def _notify_callbacks(self, event: str, data):
        for callback in self.callbacks:
            try:
                callback(event, data)
            except Exception as e:
                logger.error(f"Callback error: {str(e)}")
    
    def save_accounts(self):
        data = {}
        for acc_id, account in self.accounts.items():
            data[acc_id] = {
                "id": account.id,
                "name": account.name,
                "device_id": account.device_id,
                "username": account.username,
                "password": account.password,
                # "server": account.server,
                # "character": account.character,
                "settings": account.settings
            }
        
        with open(self.accounts_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
    
    def load_accounts(self):
        if not os.path.exists(self.accounts_file):
            logger.info("No accounts file found")
            return

        try:
            with open(self.accounts_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

            for acc_id, acc_data in data.items():
                account = Account(
                    id=acc_data["id"],
                    name=acc_data["name"],
                    device_id=acc_data["device_id"],
                    username=acc_data["username"],
                    password=acc_data["password"],
                    # server=acc_data.get("server", ""),
                    # character=acc_data.get("character", ""),
                    settings=acc_data.get("settings", {})
                )
                self.accounts[acc_id] = account
                logger.info(f"Loaded account: {account.name} ({account.id})")

            logger.info(f"Total {len(self.accounts)} accounts loaded")
        except Exception as e:
            logger.error(f"Error loading accounts: {str(e)}")