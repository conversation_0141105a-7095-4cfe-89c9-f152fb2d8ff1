import subprocess
import re
import time
from typing import List, Optional, Tuple
import logging
from PIL import Image
import numpy as np
import cv2
import io
from ..config.config import load_config
from ..utils.resolution_utils import resolution_scaler
import os
import threading

logger = logging.getLogger(__name__)


class ADBController:
    def __init__(self):
        config = load_config()
        self.adb_path = config.get("adb_path", r"C:\LDPlayer\LDPlayer9\adb.exe")
        self.devices = []
        self.screenshot_lock = threading.Lock()  # Lock để đảm bảo thread-safe
        self._cleanup_temp_files()  # Dọn dẹp file tạm thời cũ

    def _cleanup_temp_files(self):
        """Dọn dẹp các file screenshot tạm thời cũ"""
        temp_dir = "temp_screenshots"
        if os.path.exists(temp_dir):
            try:
                for filename in os.listdir(temp_dir):
                    if filename.startswith("screen_temp_") and filename.endswith(".png"):
                        file_path = os.path.join(temp_dir, filename)
                        os.remove(file_path)
                        logger.debug(f"Cleaned up temp file: {file_path}")
            except Exception as e:
                logger.warning(f"Error cleaning temp files: {str(e)}")

    def execute_command(self, command: str, device_id: Optional[str] = None) -> str:
        cmd = [self.adb_path]
        if device_id:
            cmd.extend(["-s", device_id])
        cmd.extend(command.split())
        
        try:
            logger.info(f"Executing ADB command: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            if result.returncode != 0:
                logger.error(f"ADB command failed: {result.stderr}")
                return ""
            return result.stdout
        except subprocess.TimeoutExpired:
            logger.error(f"ADB command timeout: {' '.join(cmd)}")
            return ""
        except Exception as e:
            logger.error(f"ADB command error: {str(e)}")
            return ""
    
    def refresh_devices(self) -> List[str]:
        """
        Duyệt qua các port để tìm thiết bị ADB
        Sử dụng cấu hình port range từ config
        """
        config = load_config()
        devices = []

        # Lấy cấu hình port range
        port_config = config.get("adb_port_range", {})
        start_port = port_config.get("start_port", 5555)
        end_port = port_config.get("end_port", 5575)
        step = port_config.get("step", 2)
        connection_timeout = config.get("adb_connection_timeout", 3)
        fallback_enabled = config.get("adb_fallback_to_devices", True)

        logger.info(f"Scanning ports from {start_port} to {end_port} (step: {step})")

        # Duyệt qua các port trong range
        for port in range(start_port, end_port + 1, step):
            device_address = f"127.0.0.1:{port}"

            try:
                logger.debug(f"Trying to connect to {device_address}")

                # Thử kết nối với timeout
                connect_result = self._try_connect_device(device_address, connection_timeout)

                if connect_result:
                    # Kiểm tra xem thiết bị có thực sự hoạt động không
                    if self._verify_device_responsive(device_address):
                        logger.info(f"Found active device at {device_address}")
                        devices.append(device_address)
                    else:
                        logger.debug(f"Device at {device_address} connected but not responsive")
                        # Ngắt kết nối thiết bị không hoạt động
                        self._disconnect_device(device_address)

            except Exception as e:
                logger.debug(f"Failed to connect to {device_address}: {str(e)}")
                continue

        # Fallback: sử dụng adb devices nếu không tìm thấy thiết bị nào
        if not devices and fallback_enabled:
            logger.info("No devices found via port scanning, falling back to adb devices")
            devices = self._get_devices_from_adb_command()

        self.devices = devices
        logger.info(f"Found {len(devices)} devices: {devices}")
        return devices

    def _try_connect_device(self, device_address: str, timeout: int) -> bool:
        """
        Thử kết nối đến một thiết bị ADB
        """
        try:
            # Tạo command với timeout
            cmd = [self.adb_path, "connect", device_address]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=timeout)

            if result.returncode == 0:
                output = result.stdout.lower()
                return "connected" in output or "already connected" in output
            else:
                logger.debug(f"Connect failed for {device_address}: {result.stderr}")
                return False

        except subprocess.TimeoutExpired:
            logger.debug(f"Connect timeout for {device_address}")
            return False
        except Exception as e:
            logger.debug(f"Connect error for {device_address}: {str(e)}")
            return False

    def _verify_device_responsive(self, device_address: str) -> bool:
        """
        Kiểm tra xem thiết bị có phản hồi shell command không
        """
        try:
            # Thử một command đơn giản để kiểm tra thiết bị có hoạt động không
            test_result = self.execute_command("shell echo test", device_address)
            return test_result.strip() == "test"
        except Exception as e:
            logger.debug(f"Device verification failed for {device_address}: {str(e)}")
            return False

    def _disconnect_device(self, device_address: str):
        """
        Ngắt kết nối thiết bị
        """
        try:
            cmd = [self.adb_path, "disconnect", device_address]
            subprocess.run(cmd, capture_output=True, text=True, timeout=3)
            logger.debug(f"Disconnected from {device_address}")
        except Exception as e:
            logger.debug(f"Failed to disconnect from {device_address}: {str(e)}")

    def _get_devices_from_adb_command(self) -> List[str]:
        """
        Fallback method: sử dụng 'adb devices' để lấy danh sách thiết bị
        """
        devices = []
        try:
            output = self.execute_command("devices")
            for line in output.strip().split('\n')[1:]:
                if '\t' in line:
                    device_id, status = line.split('\t')
                    if status == 'device':
                        devices.append(device_id)
        except Exception as e:
            logger.error(f"Failed to get devices from adb command: {str(e)}")

        return devices

    def get_device_display_name(self, device_id: str) -> str:
        """
        Lấy tên hiển thị của thiết bị cho giao diện người dùng
        """
        # Ưu tiên tạo tên từ port để đảm bảo tính nhất quán
        if "127.0.0.1:" in device_id:
            port = device_id.split(":")[-1]
            try:
                port_num = int(port)
                if port_num >= 5555:
                    instance_num = ((port_num - 5555) // 2) + 1
                    return f"LDPlayer-{instance_num}"
            except ValueError:
                pass

        # Fallback: thử lấy tên từ device properties
        device_name = self._get_device_name(device_id)
        if device_name and "LDPlayer" in device_name:
            return device_name

        # Cuối cùng trả về device_id
        return device_id

    def _get_device_name(self, device_id: str) -> Optional[str]:
        """
        Lấy tên thiết bị từ system properties
        """
        try:
            # Thử lấy tên LDPlayer từ các properties khác nhau
            properties_to_check = [
                "ro.ldplayer.name",
                "ro.ldplayer.instance",
                "ro.product.device",
                "ro.product.model",
                "ro.product.name"
            ]

            for prop in properties_to_check:
                output = self.execute_command(f"shell getprop {prop}", device_id)
                if output and output.strip():
                    result = output.strip()
                    # Nếu có chứa "LDPlayer" hoặc "LD" thì ưu tiên
                    if "LDPlayer" in result or "LD" in result:
                        return result

            return None
        except Exception as e:
            logger.debug(f"Failed to get device name for {device_id}: {str(e)}")
            return None

    def tap(self, device_id: str, x: int, y: int) -> bool:
        output = self.execute_command(f"shell input tap {x} {y}", device_id)
        return output is not None

    def tap_scaled(self, device_id: str, x: int, y: int) -> bool:
        """
        Tap với tọa độ được scale từ base resolution (1280x720)
        """
        scaled_x, scaled_y = resolution_scaler.scale_coordinates(self, device_id, x, y)
        return self.tap(device_id, scaled_x, scaled_y)
    
    def swipe(self, device_id: str, x1: int, y1: int, x2: int, y2: int, duration: int = 300) -> bool:
        output = self.execute_command(f"shell input swipe {x1} {y1} {x2} {y2} {duration}", device_id)
        return output is not None

    def swipe_scaled(self, device_id: str, x1: int, y1: int, x2: int, y2: int, duration: int = 300) -> bool:
        """
        Swipe với tọa độ được scale từ base resolution (1280x720)
        """
        scaled_x1, scaled_y1 = resolution_scaler.scale_coordinates(self, device_id, x1, y1)
        scaled_x2, scaled_y2 = resolution_scaler.scale_coordinates(self, device_id, x2, y2)
        return self.swipe(device_id, scaled_x1, scaled_y1, scaled_x2, scaled_y2, duration)
    
    def input_text(self, device_id: str, text: str, clear_before: bool = True) -> bool:
        if clear_before:
            for _ in range(20):  # Xoá tối đa 50 ký tự
                self.execute_command("shell input keyevent KEYCODE_DEL", device_id)

        text = text.replace(' ', '%s')
        output = self.execute_command(f"shell input text {text}", device_id)
        self.execute_command("shell input keyevent KEYCODE_ENTER", device_id)
        return output is not None
    
    def screenshot(self, device_id: str) -> Optional[np.ndarray]:
        with self.screenshot_lock:
            # output = subprocess.run(
            #     [self.adb_path, "-s", device_id, "exec-out", "screencap", "-p"],
            #     capture_output=True
            # )
            # if output.returncode == 0:
            #     img = Image.open(io.BytesIO(output.stdout))
            #     return cv2.cvtColor(np.array(img), cv2.COLOR_RGB2BGR)

            # Legacy - tạo tên file riêng cho mỗi device để tránh xung đột
            device_safe = device_id.replace(":", "_").replace(".", "_")
            remote_path = f"/sdcard/screen_temp_{device_safe}.png"

            # Tạo thư mục temp nếu chưa có
            temp_dir = "temp_screenshots"
            if not os.path.exists(temp_dir):
                os.makedirs(temp_dir)

            local_path = os.path.join(temp_dir, f"screen_temp_{device_safe}.png")

            try:
                subprocess.run([self.adb_path, "-s", device_id, "shell", "screencap", "-p", remote_path])
                subprocess.run([self.adb_path, "-s", device_id, "pull", remote_path, local_path])

                if os.path.exists(local_path):
                    img = cv2.imread(local_path)
                    os.remove(local_path)
                    # Cleanup remote file
                    subprocess.run([self.adb_path, "-s", device_id, "shell", "rm", remote_path])
                    return img
            except Exception as e:
                logger.error(f"Screenshot error for device {device_id}: {str(e)}")
                # Cleanup files if they exist
                if os.path.exists(local_path):
                    try:
                        os.remove(local_path)
                    except:
                        pass

            return None
    
    def get_screen_size(self, device_id: str) -> Tuple[int, int]:
        output = self.execute_command("shell wm size", device_id)
        match = re.search(r'(\d+)x(\d+)', output)
        if match:
            return int(match.group(1)), int(match.group(2))
        return 1920, 1080
    
    def start_app(self, device_id: str, package_name: str, activity_name: str) -> bool:
        output = self.execute_command(f"shell am start -n {package_name}/{activity_name}", device_id)
        return output is not None
    
    def stop_app(self, device_id: str, package_name: str) -> bool:
        output = self.execute_command(f"shell am force-stop {package_name}", device_id)
        return output is not None
    
    def is_app_running(self, device_id: str, package_name: str) -> bool:
        output = self.execute_command(f"shell pidof {package_name}", device_id)
        return bool(output.strip())