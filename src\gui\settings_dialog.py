from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QTabWidget,
                             QWidget, QFormLayout, QSpinBox, QCheckBox,
                             QLineEdit, QPushButton, QDialogButtonBox,
                             QGroupBox, QComboBox, QLabel, QTextEdit)
from PyQt5.QtCore import Qt
import json
import os
from src.config.config import load_config

class SettingsDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.settings_file = "settings.json"
        self.settings = self.load_settings()
        self.config = load_config()
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle("Cài đặt")
        self.setModal(True)
        self.resize(600, 500)
        
        layout = QVBoxLayout()
        
        self.tabs = QTabWidget()
        
        # General settings
        general_tab = self.create_general_tab()
        self.tabs.addTab(general_tab, "Chung")
        
        # Auto settings
        auto_tab = self.create_auto_tab()
        self.tabs.addTab(auto_tab, "Tự động")
        
        # Game settings
        game_tab = self.create_game_tab()
        self.tabs.addTab(game_tab, "Game")

        # Auto Train settings
        train_tab = self.create_train_tab()
        self.tabs.addTab(train_tab, "Auto Train")

        layout.addWidget(self.tabs)
        
        # Buttons
        button_box = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel,
            Qt.Horizontal, self
        )
        button_box.accepted.connect(self.save_settings)
        button_box.rejected.connect(self.reject)
        
        layout.addWidget(button_box)
        self.setLayout(layout)
    
    def create_general_tab(self):
        widget = QWidget()
        layout = QVBoxLayout()
        
        # ADB settings
        adb_group = QGroupBox("Cài đặt ADB")
        adb_layout = QFormLayout()
        
        self.adb_path_edit = QLineEdit(self.settings.get("adb_path", self.config.get("adb_path")))
        adb_layout.addRow("Đường dẫn ADB:", self.adb_path_edit)
        
        # self.screenshot_interval = QSpinBox()
        # self.screenshot_interval.setRange(100, 5000)
        # self.screenshot_interval.setSuffix(" ms")
        # self.screenshot_interval.setValue(self.settings.get("screenshot_interval", self.config.get("screenshot_interval")))
        # adb_layout.addRow("Khoảng cách chụp màn hình:", self.screenshot_interval)
        
        adb_group.setLayout(adb_layout)
        layout.addWidget(adb_group)
        
        # Log settings
        log_group = QGroupBox("Cài đặt nhật ký")
        log_layout = QFormLayout()
        
        self.log_level_combo = QComboBox()
        self.log_level_combo.addItems(["DEBUG", "INFO", "WARNING", "ERROR"])
        self.log_level_combo.setCurrentText(self.settings.get("log_level", self.config.get("log_level")))
        log_layout.addRow("Mức độ log:", self.log_level_combo)
        
        self.save_log_check = QCheckBox()
        self.save_log_check.setChecked(self.settings.get("save_log", self.config.get("save_log")))
        log_layout.addRow("Lưu log vào file:", self.save_log_check)
        
        log_group.setLayout(log_layout)
        layout.addWidget(log_group)
        
        layout.addStretch()
        widget.setLayout(layout)
        return widget
    
    def create_auto_tab(self):
        widget = QWidget()
        layout = QVBoxLayout()
        
        # Auto login
        login_group = QGroupBox("Tự động đăng nhập")
        login_layout = QFormLayout()
        
        self.auto_login_check = QCheckBox()
        self.auto_login_check.setChecked(self.settings.get("auto_login", self.config.get("auto_login")))
        login_layout.addRow("Tự động đăng nhập:", self.auto_login_check)
        
        self.login_retry = QSpinBox()
        self.login_retry.setRange(1, 10)
        self.login_retry.setValue(self.settings.get("login_retry", self.config.get("login_retry")))
        login_layout.addRow("Số lần thử lại:", self.login_retry)
        
        login_group.setLayout(login_layout)
        layout.addWidget(login_group)
        
        # Auto tasks
        task_group = QGroupBox("Nhiệm vụ tự động")
        task_layout = QFormLayout()
        
        # self.auto_daily_check = QCheckBox()
        # self.auto_daily_check.setChecked(self.settings.get("auto_daily", self.config.get("auto_daily")))
        # task_layout.addRow("Nhiệm vụ hàng ngày:", self.auto_daily_check)
        
        # self.auto_event_check = QCheckBox()
        # self.auto_event_check.setChecked(self.settings.get("auto_event", self.config.get("auto_event")))
        # task_layout.addRow("Nhiệm vụ sự kiện:", self.auto_event_check)
        
        # self.auto_dungeon_check = QCheckBox()
        # self.auto_dungeon_check.setChecked(self.settings.get("auto_dungeon", self.config.get("auto_dungeon")))
        # task_layout.addRow("Tự động phó bản:", self.auto_dungeon_check)

        self.auto_train_check = QCheckBox()
        self.auto_train_check.setChecked(self.settings.get("auto_train", self.config.get("auto_train")))
        task_layout.addRow("Auto train:", self.auto_train_check)
        
        task_group.setLayout(task_layout)
        layout.addWidget(task_group)
        
        layout.addStretch()
        widget.setLayout(layout)
        return widget
    
    def create_game_tab(self):
        widget = QWidget()
        layout = QVBoxLayout()
        
        # Game info
        game_group = QGroupBox("Thông tin game")
        game_layout = QFormLayout()
        
        self.package_name_edit = QLineEdit(
            self.settings.get("package_name", self.config.get("package_name"))
        )
        game_layout.addRow("Package name:", self.package_name_edit)
        
        self.activity_name_edit = QLineEdit(
            self.settings.get("activity_name", self.config.get("activity_name"))
        )
        game_layout.addRow("Activity name:", self.activity_name_edit)
        
        game_group.setLayout(game_layout)
        layout.addWidget(game_group)
        
        # # Coordinates
        # coord_group = QGroupBox("Tọa độ cơ bản")
        # coord_layout = QFormLayout()
        
        # self.login_btn_x = QSpinBox()
        # self.login_btn_x.setRange(0, 9999)
        # self.login_btn_x.setValue(self.settings.get("login_btn_x", self.config.get("login_btn_x")))
        
        # self.login_btn_y = QSpinBox()
        # self.login_btn_y.setRange(0, 9999)
        # self.login_btn_y.setValue(self.settings.get("login_btn_y", self.config.get("login_btn_y")))
        
        # coord_layout.addRow("Nút đăng nhập X:", self.login_btn_x)
        # coord_layout.addRow("Nút đăng nhập Y:", self.login_btn_y)
        
        # coord_group.setLayout(coord_layout)
        # layout.addWidget(coord_group)
        
        layout.addStretch()
        widget.setLayout(layout)
        return widget

    def create_train_tab(self):
        widget = QWidget()
        layout = QVBoxLayout()

        # Train area settings
        area_group = QGroupBox("Cài đặt khu vực luyện công")
        area_layout = QFormLayout()

        self.train_area_combo = QComboBox()
        train_areas = self.config.get('train_areas')

        for area_id, area_data in train_areas.items():
            self.train_area_combo.addItem(area_data['name'], area_id)

        # Set current selection
        current_area = self.settings.get('train_area', self.config.get('train_area'))
        for i in range(self.train_area_combo.count()):
            if self.train_area_combo.itemData(i) == current_area:
                self.train_area_combo.setCurrentIndex(i)
                break

        area_layout.addRow("Khu vực:", self.train_area_combo)

        self.train_map_combo = QComboBox()
        self.train_area_combo.currentTextChanged.connect(self.update_train_maps)
        self.update_train_maps()  # Load initial maps

        area_layout.addRow("Map:", self.train_map_combo)
        area_group.setLayout(area_layout)
        layout.addWidget(area_group)

        # Train position settings
        position_group = QGroupBox("Vị trí luyện công")
        position_layout = QFormLayout()

        self.train_x = QSpinBox()
        self.train_x.setRange(0, 9999)
        self.train_x.setValue(self.settings.get('train_x', self.config.get('train_x')))
        position_layout.addRow("Tọa độ X:", self.train_x)

        self.train_y = QSpinBox()
        self.train_y.setRange(0, 9999)
        self.train_y.setValue(self.settings.get('train_y', self.config.get('train_y')))
        position_layout.addRow("Tọa độ Y:", self.train_y)

        position_group.setLayout(position_layout)
        layout.addWidget(position_group)

        # Auto fight settings
        # fight_group = QGroupBox("Cài đặt auto đánh")
        # fight_layout = QFormLayout()

        # self.auto_fight_enabled = QCheckBox()
        # self.auto_fight_enabled.setChecked(self.settings.get('auto_fight_enabled', self.config.get('auto_fight_enabled')))
        # fight_layout.addRow("Bật auto đánh:", self.auto_fight_enabled)

        # fight_group.setLayout(fight_layout)
        # layout.addWidget(fight_group)

        layout.addStretch()
        widget.setLayout(layout)
        return widget

    def update_train_maps(self):
        """Update train maps based on selected area"""
        self.train_map_combo.clear()

        from ..config.config import load_config
        config = load_config()
        train_areas = config.get('train_areas', {})

        current_area = self.train_area_combo.currentData()
        if current_area and current_area in train_areas:
            maps = train_areas[current_area].get('maps', {})
            for map_id, map_data in maps.items():
                self.train_map_combo.addItem(map_data['name'], map_id)

        # Set current selection
        current_map = self.settings.get('train_map', config.get('train_map'))
        for i in range(self.train_map_combo.count()):
            if self.train_map_combo.itemData(i) == current_map:
                self.train_map_combo.setCurrentIndex(i)
                break

    def load_settings(self):
        if os.path.exists(self.settings_file):
            try:
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                pass
        return {}
    
    def save_settings(self):
        self.settings = {
            # General
            "adb_path": self.adb_path_edit.text(),
            # "screenshot_interval": self.screenshot_interval.value(),
            "log_level": self.log_level_combo.currentText(),
            "save_log": self.save_log_check.isChecked(),
            
            # Auto
            "auto_login": self.auto_login_check.isChecked(),
            "login_retry": self.login_retry.value(),
            # "auto_daily": self.auto_daily_check.isChecked(),
            # "auto_event": self.auto_event_check.isChecked(),
            # "auto_dungeon": self.auto_dungeon_check.isChecked(),
            "auto_train": self.auto_train_check.isChecked(),
            
            # Game
            "package_name": self.package_name_edit.text(),
            "activity_name": self.activity_name_edit.text(),
            # "login_btn_x": self.login_btn_x.value(),
            # "login_btn_y": self.login_btn_y.value(),

            # Auto Train
            "train_area": self.train_area_combo.currentData(),
            "train_map": self.train_map_combo.currentData(),
            "train_x": self.train_x.value(),
            "train_y": self.train_y.value(),
            # "auto_fight_enabled": self.auto_fight_enabled.isChecked(),
        }
        
        with open(self.settings_file, 'w', encoding='utf-8') as f:
            json.dump(self.settings, f, ensure_ascii=False, indent=2)
        
        self.accept()