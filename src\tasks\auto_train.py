import time
import logging
from typing import Optional, Tuple
from ..core.account_manager import Account
from ..core.adb_controller import ADBController
from ..utils.image_utils import find_image, wait_for_image, wait_for_image_disappear
from ..config.config import load_config

logger = logging.getLogger(__name__)


class AutoTrainManager:
    def __init__(self, adb: ADBController):
        self.adb = adb
        self.config = load_config()
        
    def start_auto_train(self, account: Account, train_area: str = None, 
                        train_map: str = None, train_x: int = None, train_y: int = None) -> bool:
        """
        Bắt đầu auto train cho account
        """
        try:
            area = train_area or self.config.get('train_area')
            map_id = train_map or self.config.get('train_map')
            pos_x = train_x or self.config.get('train_x')
            pos_y = train_y or self.config.get('train_y')
            
            logger.info(f"Starting auto train for {account.name} - Area: {area}, Map: {map_id}, Position: ({pos_x}, {pos_y})")
            
            # Bước 1: <PERSON><PERSON><PERSON> tra có đang ở map train không
            if not self._is_in_train_map(account.device_id, area, map_id):
                logger.info(f"Not in train map, navigating to train area for {account.name}")
                
                # Bước 2: Di chuyển đến xa phu
                if not self._move_to_xa_phu(account.device_id):
                    logger.error(f"Failed to move to xa phu for {account.name}")
                    return False
                
                # Bước 3: Chọn map luyện công
                if not self._select_train_map(account.device_id, area, map_id):
                    logger.error(f"Failed to select train map {map_id} for {account.name}")
                    return False
            
            # Bước 4: Setup vị trí x y trên map
            if not self._setup_train_position(account.device_id, pos_x, pos_y):
                logger.error(f"Failed to setup train position for {account.name}")
                return False
            
            # Bước 5: Bật auto đánh
            if not self._enable_auto_fight(account.device_id):
                logger.error(f"Failed to enable auto fight for {account.name}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error starting auto train for {account.name}: {str(e)}")
            return False
    
    def _is_in_train_map(self, device_id: str, area: str, map_id: str) -> bool:
        """
        Kiểm tra có đang ở map train không
        """
        screenshot = self.adb.screenshot(device_id)
        if screenshot is None:
            return False
        
        map_template = f"templates/train_map_{area}_{map_id}_loaded.png"
        map_pos = find_image(screenshot, map_template)
        if map_pos:
            logger.info("Already in train map")
            return True
        
        return False
    
    def _move_to_xa_phu(self, device_id: str) -> bool:
        """
        Di chuyển đến xa phu
        """
        # Mở minimap
        if not self.open_minimap(device_id):
            logger.error("Failed to open minimap")
            return False
        
        # Nhập tìm xa phu
        screenshot = self.adb.screenshot(device_id)
        search_pos = find_image(screenshot, "templates/enter_npc.png")
        if search_pos:
            self.adb.tap(device_id, search_pos[0], search_pos[1])
            time.sleep(0.5)
            self.adb.input_text(device_id, "xa phu")
            time.sleep(1)
            xa_phu_pos = self.config.get('xa_phu_pos')
            self.adb.tap(device_id, xa_phu_pos[0], xa_phu_pos[1])
            time.sleep(20)
            
            # Đợi di chuyển hoàn thành
            if not wait_for_image(self.adb, device_id, "templates/xa_phu.png", timeout=30):
                logger.error("Failed to move to xa phu")
                return False
            logger.info("Arrived at xa phu")
            screenshot = self.adb.screenshot(device_id)
            xa_phu_pos = find_image(screenshot, "templates/xa_phu.png")
            self.adb.tap(device_id, xa_phu_pos[0], xa_phu_pos[1])
            time.sleep(0.5)
            return True
        
        return False
    
    def _select_train_area(self, device_id: str, area: str) -> bool:
        """
        Chọn khu vực luyện công
        """
        # Click vào NPC xa phu
        screenshot = self.adb.screenshot(device_id)
        if screenshot is None:
            return False
        
        xa_phu_npc = find_image(screenshot, "templates/xa_phu_npc.png")
        if xa_phu_npc:
            self.adb.tap(device_id, xa_phu_npc[0], xa_phu_npc[1])
            time.sleep(2)
            
            # Tìm và click vào khu vực train
            area_template = f"templates/train_area_{area}.png"
            if not wait_for_image(self.adb, device_id, area_template, timeout=10):
                logger.error(f"Failed to find train area: {area}")
                return False
            area_pos = find_image(self.adb.screenshot(device_id), area_template)
            if area_pos:
                self.adb.tap(device_id, area_pos[0], area_pos[1])
                time.sleep(2)
                logger.info(f"Selected train area: {area}")
                return True
        
        logger.error(f"Failed to select train area: {area}")
        return False
    
    def _select_train_map(self, device_id: str, area: str, map_id: str) -> bool:
        """
        Chọn map luyện công cụ thể
        """
        screenshot = self.adb.screenshot(device_id)
        if screenshot is None:
            return False
        
        pos = find_image(screenshot, "templates/go_past.png")
        logger.info(f"Go past pos: {pos}")
        if pos:
            self.adb.tap(device_id, pos[0], pos[1])
            time.sleep(0.5)
            self.adb.tap(device_id, 350, 440)
            time.sleep(1)
            map_template = f"templates/train_map_{area}_{map_id}_loaded.png"
            if wait_for_image(self.adb, device_id, map_template, timeout=20):
                logger.info(f"Successfully entered train map: {map_id}")
            return True


        # train_pos = find_image(screenshot, "templates/train_area.png")
        # logger.info(f"Train pos: {train_pos}")
        # if train_pos:
        #     self.adb.tap(device_id, train_pos[0], train_pos[1])
        #     time.sleep(1)
        #     screenshot = self.adb.screenshot(device_id)
        #     area_template = f"templates/train_area_{area}.png"
        #     train_area_pos = find_image(self.adb.screenshot(device_id), area_template)
        #     if train_area_pos:
        #         self.adb.tap(device_id, train_area_pos[0], train_area_pos[1])
        #         time.sleep(1)
        #         screenshot = self.adb.screenshot(device_id)
        #         map_template = f"templates/train_map_{area}_{map_id}.png"
        #         map_pos = find_image(self.adb.screenshot(device_id), map_template)
        #         if map_pos:
        #             self.adb.tap(device_id, map_pos[0], map_pos[1])
        #             time.sleep(1)
        #             # Đợi load map
        #             map_template = f"templates/train_map_{area}_{map_id}_loaded.png"
        #             if wait_for_image(self.adb, device_id, map_template, timeout=20):
        #                 logger.info(f"Successfully entered train map: {map_id}")
        #             return True

        logger.error(f"Failed to select train map: {map_id}")
        return False
    
    def _setup_train_position(self, device_id: str, x: int, y: int) -> bool:
        """
        Setup vị trí x y để train
        """
       # Mở minimap
        if not self.open_minimap(device_id):
            logger.error("Failed to open minimap")
            return False
        
        # Set location
        screenshot = self.adb.screenshot(device_id)
        location_pos = find_image(screenshot, "templates/enter_location.png")
        if location_pos:
            self.adb.tap(device_id, location_pos[0], location_pos[1])
            time.sleep(0.5)
            screenshot = self.adb.screenshot(device_id)

            # Nhập x (sử dụng base coordinates)
            x_pos = find_image(screenshot, "templates/enter_location_x.png")
            if x_pos:
                self.adb.tap(device_id, x_pos[0], x_pos[1])
                time.sleep(0.5)
                self.adb.input_text(device_id, str(x))
                time.sleep(0.5)

            # Nhập y (sử dụng base coordinates)
            y_pos = find_image(screenshot, "templates/enter_location_y.png")
            if y_pos:
                self.adb.tap(device_id, y_pos[0], y_pos[1])
                time.sleep(0.5)
                self.adb.input_text(device_id, str(y))
                time.sleep(0.5)
            
            # Xác nhận
            confirm_pos = find_image(screenshot, "templates/confirm_location.png")
            if confirm_pos:
                self.adb.tap(device_id, confirm_pos[0], confirm_pos[1])
                time.sleep(1)

            # Đợi di chuyển hoàn thành
            if wait_for_image_disappear(self.adb, device_id, "templates/di_chuyen.png"):
                close_pos = find_image(screenshot, "templates/close_map.png")
                if close_pos:
                    self.adb.tap(device_id, close_pos[0], close_pos[1])
                    time.sleep(1)
            
            logger.info("Location set successfully")
            return True

        return False
    
    def _enable_auto_fight(self, device_id: str) -> bool:
        """
        Bật auto đánh
        """
        screenshot = self.adb.screenshot(device_id)
        if screenshot is None:
            return False
        
        # Tìm và bật auto fight button
        auto_fight_button = find_image(screenshot, "templates/auto_fight_button.png", False)
        if auto_fight_button:
            self.adb.tap(device_id, auto_fight_button[0], auto_fight_button[1])
            time.sleep(1)
            
            # Kiểm tra auto fight đã được bật
            if find_image(self.adb.screenshot(device_id), "templates/auto_fight_active.png", False):
                logger.info("Auto fight enabled successfully")
                return True
        
        logger.error("Failed to enable auto fight")
        return False
    
    def stop_auto_train(self, device_id: str) -> bool:
        """
        Dừng auto train
        """
        screenshot = self.adb.screenshot(device_id)
        if screenshot is None:
            return False
        
        # Tắt auto fight
        auto_fight_button = find_image(screenshot, "templates/auto_fight_button.png", False)
        if auto_fight_button:
            self.adb.tap(device_id, auto_fight_button[0], auto_fight_button[1])
            time.sleep(1)
            logger.info("Auto train stopped")
            return True
        
        return False
    
    def check_train_status(self, device_id: str) -> dict:
        """
        Kiểm tra trạng thái train
        """
        screenshot = self.adb.screenshot(device_id)
        if screenshot is None:
            return {"status": "error", "message": "Cannot take screenshot"}
        
        in_train = True
        pos = find_image(screenshot, "templates/dai_ly_phu.png")
        if pos:
            in_train = False
        pos = find_image(screenshot, "templates/tuong_duong.png")
        if pos:
            in_train = False

        status = {
            "auto_fight_active": bool(find_image(screenshot, "templates/auto_fight_active.png", False )),
            "in_train": in_train
            # "in_combat": bool(find_image(screenshot, "templates/in_combat.png")),
            # "hp_low": bool(find_image(screenshot, "templates/hp_low.png")),
            # "mp_low": bool(find_image(screenshot, "templates/mp_low.png")),
            # "inventory_full": bool(find_image(screenshot, "templates/inventory_full.png"))
        }
        
        return status

    def open_minimap(self, device_id: str) -> bool:
        """
        Mở mini map
        """
        screenshot = self.adb.screenshot(device_id)
        if screenshot is None:
            return False
        
        minimap_pos = self.config.get('minimap_pos')
        if minimap_pos:
            self.adb.tap(device_id, minimap_pos[0], minimap_pos[1])
            time.sleep(1)
            return True

        return False