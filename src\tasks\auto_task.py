import time
import logging
import threading
from ..core.account_manager import Account
from ..core.adb_controller import ADBController
from ..utils.image_utils import find_image, wait_for_image
from ..config.config import load_config
from .auto_train import AutoTrainManager

logger = logging.getLogger(__name__)


def auto_task(account: Account, adb: ADBController,
              stop_event: threading.Event, pause_event: threading.Event):
    """
    Main auto task function for each account
    """
    config = load_config()
    
    try:        
        # Main auto loop
        while not stop_event.is_set():
            if pause_event.is_set():
                time.sleep(1)
                continue
            
            # Check if game is running
            if not adb.is_app_running(account.device_id, config['package_name']):
                logger.info(f"Starting game for account {account.name}")
                adb.start_app(account.device_id, config['package_name'], config['activity_name'])
                
                # Wait for game to load
                if not wait_for_image(adb, account.device_id, "templates/login_button.png", timeout=10):
                    logger.error(f"Failed to load game for {account.name}")
                    time.sleep(5)
                    continue

            # Check is login screen
            screenshot = adb.screenshot(account.device_id)
            if screenshot is None:
                logger.error(f"Failed to take screenshot for {account.name} in login screen")
                time.sleep(5)
                continue
            login_pos = find_image(screenshot, "templates/login_button.png")
            if login_pos:
                if config.get('auto_login'):
                    login_success = auto_login(account, adb, config)
                    if not login_success:
                        logger.error(f"Login failed for account {account.name}")
                        time.sleep(5)
                        continue
            
            # Check enter to game screen
            screenshot = adb.screenshot(account.device_id)
            if screenshot is None:
                logger.error(f"Failed to take screenshot for {account.name} in enter game screen")
                time.sleep(5)
                continue
            enter_game_pos = find_image(screenshot, "templates/enter_game_button.png")
            if enter_game_pos:
                adb.tap(account.device_id, enter_game_pos[0], enter_game_pos[1])
                if not wait_for_image(adb, account.device_id, "templates/setting.png", timeout=10):
                    logger.error(f"Failed to enter game for {account.name}")
                    time.sleep(5)
                    continue
            logger.info(f"Entered game for {account.name}")
            # Check and perform auto tasks based on settings
            # if account.settings.get('auto_daily', True):
            #     check_daily_tasks(account, adb, screenshot)
            
            # if account.settings.get('auto_event', True):
            #     check_event_tasks(account, adb, screenshot)
            
            # if account.settings.get('auto_dungeon', False):
            #     check_dungeon_tasks(account, adb, screenshot)
            if config.get('auto_train'):
                check_train_tasks(account, adb, screenshot)
            
            # Add more auto features here
            
            time.sleep(config.get('loop_delay', 3))
            
    except Exception as e:
        logger.error(f"Error in auto task for {account.name}: {str(e)}")
        raise

def auto_login(account: Account, adb: ADBController, config: dict) -> bool:
    """
    Auto login function
    """
    max_retry = config.get('login_retry', 3)
    
    for attempt in range(max_retry):
        logger.info(f"Login attempt {attempt + 1} for {account.name}")
        
        # Take screenshot
        screenshot = adb.screenshot(account.device_id)
        if screenshot is None:
            logger.error(f"Failed to take screenshot for {account.name}")
            time.sleep(5)
            continue
        
        # Check if already logged in
        if find_image(screenshot, "templates/enter_game_button.png"):
            logger.info(f"Already logged in for {account.name}")
            return True
        
        # Find login button
        login_pos = find_image(screenshot, "templates/login_button.png")
        if login_pos:
            # Enter username
            # username_pos = find_image(screenshot, "templates/username_field.png")
            username_x = config.get('username_x')
            username_y = config.get('username_y')
            if username_x and username_y:
                adb.tap(account.device_id, username_x, username_y)
                time.sleep(0.5)
                adb.input_text(account.device_id, account.username)
                time.sleep(1)

            # Enter password
            # password_pos = find_image(screenshot, "templates/password_field.png")
            password_x = config.get('password_x')
            password_y = config.get('password_y')
            if password_x and password_y:
                adb.tap(account.device_id, password_x, password_y)
                time.sleep(0.5)
                adb.input_text(account.device_id, account.password)
                time.sleep(1)
            
            # Click login button
            adb.tap(account.device_id, login_pos[0], login_pos[1])
            time.sleep(2)
            
            if wait_for_image(adb, account.device_id, "templates/enter_game_button.png", timeout=10):
                logger.info(f"Login successful for {account.name}")
                return True
        
        time.sleep(5)
    
    return False


def check_daily_tasks(account: Account, adb: ADBController, screenshot):
    """
    Check and complete daily tasks
    """
    # Find daily task icon
    daily_pos = find_image(screenshot, "templates/daily_task_icon.png")
    if daily_pos:
        logger.info(f"Found daily tasks for {account.name}")
        adb.tap(account.device_id, daily_pos[0], daily_pos[1])
        time.sleep(2)
        
        # Auto claim rewards logic here
        # ...


def check_event_tasks(account: Account, adb: ADBController, screenshot):
    """
    Check and complete event tasks
    """
    # Event task logic here
    pass


def check_dungeon_tasks(account: Account, adb: ADBController, screenshot):
    """
    Check and complete dungeon tasks
    """
    # Dungeon task logic here
    pass


def check_train_tasks(account: Account, adb: ADBController, screenshot):
    """
    Check and complete train tasks
    """
    try:
        # Khởi tạo AutoTrainManager
        train_manager = AutoTrainManager(adb)

        # Kiểm tra trạng thái train hiện tại
        train_status = train_manager.check_train_status(account.device_id)

        # Nếu in_train == False, bắt đầu auto train
        if not train_status.get('in_train', False):
            logger.info(f"Auto fight not active for {account.name}, starting auto train")

            # Lấy settings train từ account
            train_area = account.settings.get('train_area')
            train_map = account.settings.get('train_map')
            train_x = account.settings.get('train_x')
            train_y = account.settings.get('train_y')

            # Bắt đầu auto train
            success = train_manager.start_auto_train(
                account, train_area, train_map, train_x, train_y
            )

            if success:
                logger.info(f"Auto train started for {account.name}")
            else:
                logger.error(f"Failed to start auto train for {account.name}")

        # # Kiểm tra các tình huống cần xử lý
        # if train_status.get('hp_low', False):
        #     logger.warning(f"HP low for {account.name}, may need healing")
        #     # Có thể thêm logic hồi máu ở đây

        # if train_status.get('mp_low', False):
        #     logger.warning(f"MP low for {account.name}, may need mana recovery")
        #     # Có thể thêm logic hồi mana ở đây

        # if train_status.get('inventory_full', False):
        #     logger.warning(f"Inventory full for {account.name}, may need to sell items")
        #     # Có thể thêm logic bán đồ ở đây

    except Exception as e:
        logger.error(f"Error in train tasks for {account.name}: {str(e)}")
    